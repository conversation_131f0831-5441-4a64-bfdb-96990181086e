# Guía de Solución de Problemas - OposiAI

## 🚨 Error: "Uncaught SyntaxError: Invalid or unexpected token"

### Síntomas
- La aplicación no se inicia
- Error de sintaxis en layout.js línea 315:29 (o similar)
- Pantalla en blanco o error de compilación

### Causas Comunes
1. **Problemas de caché de Next.js**
2. **Importaciones incorrectas de librerías**
3. **Configuración incorrecta de TypeScript**
4. **Archivos corruptos en node_modules**

### Soluciones

#### 1. Limpiar Caché (Solución Rápida)
```bash
# Usar el script personalizado
npm run clean

# O manualmente
rm -rf .next
rm -rf node_modules/.cache
rm tsconfig.tsbuildinfo
```

#### 2. <PERSON><PERSON>cio <PERSON>mple<PERSON>
```bash
# Limpiar y reiniciar
npm run fresh-start

# O paso a paso
npm run clean
npm run dev
```

#### 3. Reinstalar Dependencias (Si persiste el problema)
```bash
# Eliminar node_modules y reinstalar
rm -rf node_modules
rm package-lock.json
npm install
npm run dev
```

#### 4. Verificar Errores de Build
```bash
# Probar build para identificar errores específicos
npm run build
```

## 🔧 Errores Comunes Resueltos

### Error: `pdf-parse/lib/pdf-parse.js` no encontrado
**Solución:** Cambiar importación a `import('pdf-parse')` en lugar de la ruta específica.

### Error: `toast.info` no existe
**Solución:** Usar `toast()` en lugar de `toast.info()` con react-hot-toast.

### Error: `obtenerPreguntasTestCount` no encontrado
**Solución:** Agregar la función a las importaciones en el archivo correspondiente.

## 🛠️ Scripts Útiles

```bash
# Desarrollo normal
npm run dev

# Limpiar caché
npm run clean

# Limpiar y reiniciar
npm run fresh-start

# Build de producción
npm run build

# Verificar tipos TypeScript
npx tsc --noEmit

# Verificar ESLint
npm run lint
```

## 📋 Checklist de Diagnóstico

Cuando tengas problemas, verifica en este orden:

- [ ] ¿El error aparece en build o en dev?
- [ ] ¿Has limpiado la caché recientemente?
- [ ] ¿Las dependencias están actualizadas?
- [ ] ¿Hay errores de TypeScript?
- [ ] ¿Hay errores de ESLint?
- [ ] ¿Las variables de entorno están configuradas?

## 🚀 Prevención

### Buenas Prácticas
1. **Limpiar caché regularmente** después de cambios importantes
2. **Usar TypeScript estricto** para detectar errores temprano
3. **Mantener dependencias actualizadas**
4. **Usar imports dinámicos** para librerías problemáticas
5. **Verificar build** antes de hacer commit

### Configuración Recomendada
- `next.config.js` configurado para manejar importaciones problemáticas
- Scripts de limpieza automatizados
- Configuración de TypeScript estricta pero práctica

## 📞 Soporte

Si el problema persiste después de seguir esta guía:

1. Verifica los logs completos del error
2. Revisa si hay actualizaciones de Next.js
3. Consulta la documentación oficial de Next.js
4. Considera crear un issue en el repositorio del proyecto
