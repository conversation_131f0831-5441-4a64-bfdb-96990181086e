import React, { useEffect, useState } from 'react';
import {
  Test,
  PreguntaTest,
  obtenerTests,
  obtenerPreguntasPorTestId,
  obtenerPreguntasTestCount,
  registrarRespuestaTest,
  obtenerEstadisticasGeneralesTests,
  obtenerEstadisticasTest,
  EstadisticasGeneralesTest,
  EstadisticasTestEspecifico
} from '../lib/supabase';
import {
  FiBarChart2,
  FiCheck,
  FiX,
  FiClock,
  FiAward,
  FiChevronLeft,
  FiChevronRight,
  FiAlertTriangle,
  FiPieChart,
  FiTrendingUp
} from 'react-icons/fi';

export default function TestViewer() {
  const [tests, setTests] = useState<Test[]>([]);
  const [testSeleccionado, setTestSeleccionado] = useState<Test | null>(null);
  const [preguntas, setPreguntas] = useState<PreguntaTest[]>([]);
  const [preguntaActual, setPreguntaActual] = useState(0);
  const [respuestaSeleccionada, setRespuestaSeleccionada] = useState<'a' | 'b' | 'c' | 'd' | null>(null);
  const [respuestasUsuario, setRespuestasUsuario] = useState<Record<string, 'a' | 'b' | 'c' | 'd'>>({});
  const [testCompletado, setTestCompletado] = useState(false);
  const [resultados, setResultados] = useState<{
    correctas: number;
    incorrectas: number;
    porcentaje: number;
    tiempoTotal: number;
  } | null>(null);
  const [tiempoInicio, setTiempoInicio] = useState<number | null>(null);
  const [tiemposPregunta, setTiemposPregunta] = useState<Record<string, number>>({});
  const [cargando, setCargando] = useState(true);
  const [error, setError] = useState('');
  const [mostrarEstadisticasGenerales, setMostrarEstadisticasGenerales] = useState(false);
  const [mostrarEstadisticasTest, setMostrarEstadisticasTest] = useState(false);
  const [estadisticasGenerales, setEstadisticasGenerales] = useState<EstadisticasGeneralesTest | null>(null);
  const [estadisticasTest, setEstadisticasTest] = useState<EstadisticasTestEspecifico | null>(null);

  // Cargar los tests al inicio
  useEffect(() => {
    cargarTests();
  }, []);

  // Cargar las preguntas cuando se selecciona un test
  useEffect(() => {
    if (testSeleccionado) {
      cargarPreguntas(testSeleccionado.id);
    }
  }, [testSeleccionado]);

  // Iniciar el temporizador cuando se cargan las preguntas
  useEffect(() => {
    if (preguntas.length > 0 && !testCompletado) {
      setTiempoInicio(Date.now());
    }
  }, [preguntas]);

  const cargarTests = async () => {
    setCargando(true);
    setError('');
    try {
      const testsData = await obtenerTests();
      // Enrich tests with question counts
      const testsConConteos = await Promise.all(
        testsData.map(async (test) => {
          try {
            const count = await obtenerPreguntasTestCount(test.id);
            return { ...test, numero_preguntas: count };
          } catch (countError) {
            console.error(`Error al obtener conteo para test ${test.id}:`, countError);
            return { ...test, numero_preguntas: undefined }; // O algún valor por defecto o manejo de error
          }
        })
      );
      setTests(testsConConteos);
    } catch (error) {
      console.error('Error al cargar tests:', error);
      setError('No se pudieron cargar los tests. Por favor, inténtalo de nuevo.');
    } finally {
      setCargando(false);
    }
  };

  const cargarPreguntas = async (testId: string) => {
    setCargando(true);
    try {
      const preguntasData = await obtenerPreguntasPorTestId(testId);
      setPreguntas(preguntasData);
      setPreguntaActual(0);
      setRespuestaSeleccionada(null);
      setRespuestasUsuario({});
      setTestCompletado(false);
      setResultados(null);
    } catch (error) {
      console.error('Error al cargar preguntas:', error);
      setError('No se pudieron cargar las preguntas del test. Por favor, inténtalo de nuevo.');
    } finally {
      setCargando(false);
    }
  };

  const seleccionarRespuesta = (opcion: 'a' | 'b' | 'c' | 'd') => {
    if (testCompletado) return;

    setRespuestaSeleccionada(opcion);

    // Guardar el tiempo que tardó en responder
    if (tiempoInicio) {
      const tiempoActual = Date.now();
      const tiempoPregunta = tiempoActual - tiempoInicio;
      setTiemposPregunta(prev => ({
        ...prev,
        [preguntas[preguntaActual].id]: tiempoPregunta
      }));
      setTiempoInicio(tiempoActual);
    }

    // Guardar la respuesta del usuario
    setRespuestasUsuario(prev => ({
      ...prev,
      [preguntas[preguntaActual].id]: opcion
    }));
  };

  const siguientePregunta = async () => {
    if (preguntaActual < preguntas.length - 1) {
      // Guardar estadística de la pregunta actual
      await guardarEstadisticaPregunta();

      // Avanzar a la siguiente pregunta
      setPreguntaActual(preguntaActual + 1);
      setRespuestaSeleccionada(null);
    } else {
      // Es la última pregunta, finalizar el test
      await guardarEstadisticaPregunta();
      finalizarTest();
    }
  };

  const anteriorPregunta = () => {
    if (preguntaActual > 0) {
      setPreguntaActual(preguntaActual - 1);
      setRespuestaSeleccionada(respuestasUsuario[preguntas[preguntaActual - 1].id] || null);
    }
  };

  const guardarEstadisticaPregunta = async () => {
    if (!testSeleccionado || !respuestaSeleccionada) return;

    const preguntaActualObj = preguntas[preguntaActual];
    const esCorrecta = respuestaSeleccionada === preguntaActualObj.respuesta_correcta;

    try {
      await registrarRespuestaTest(
        testSeleccionado.id,
        preguntaActualObj.id,
        respuestaSeleccionada,
        esCorrecta
      );
    } catch (error) {
      console.error('Error al guardar estadística:', error);
    }
  };

  const finalizarTest = () => {
    setTestCompletado(true);

    // Calcular resultados
    let correctas = 0;
    let incorrectas = 0;
    let tiempoTotal = 0;

    preguntas.forEach(pregunta => {
      const respuestaUsuario = respuestasUsuario[pregunta.id];
      if (respuestaUsuario) {
        if (respuestaUsuario === pregunta.respuesta_correcta) {
          correctas++;
        } else {
          incorrectas++;
        }
      } else {
        incorrectas++;
      }

      tiempoTotal += tiemposPregunta[pregunta.id] || 0;
    });

    const porcentaje = (correctas / preguntas.length) * 100;

    setResultados({
      correctas,
      incorrectas,
      porcentaje,
      tiempoTotal
    });
  };

  const reiniciarTest = () => {
    setPreguntaActual(0);
    setRespuestaSeleccionada(null);
    setRespuestasUsuario({});
    setTestCompletado(false);
    setResultados(null);
    setTiempoInicio(Date.now());
    setTiemposPregunta({});
  };

  const cargarEstadisticasGenerales = async () => {
    try {
      const estadisticas = await obtenerEstadisticasGeneralesTests();
      setEstadisticasGenerales(estadisticas);
      setMostrarEstadisticasGenerales(true);
      setMostrarEstadisticasTest(false);
    } catch (error) {
      console.error('Error al cargar estadísticas generales:', error);
      setError('No se pudieron cargar las estadísticas generales. Por favor, inténtalo de nuevo.');
    }
  };

  const cargarEstadisticasTest = async (testId: string) => {
    try {
      const estadisticas = await obtenerEstadisticasTest(testId);
      setEstadisticasTest(estadisticas);
      setMostrarEstadisticasTest(true);
      setMostrarEstadisticasGenerales(false);
    } catch (error) {
      console.error('Error al cargar estadísticas del test:', error);
      setError('No se pudieron cargar las estadísticas del test. Por favor, inténtalo de nuevo.');
    }
  };

  const formatTiempo = (ms: number) => {
    const segundos = Math.floor(ms / 1000);
    const minutos = Math.floor(segundos / 60);
    const segundosRestantes = segundos % 60;
    return `${minutos}:${segundosRestantes.toString().padStart(2, '0')}`;
  };

  return (
    <div className="mt-8 border-t pt-8">
      <h2 className="text-xl font-bold mb-4">Mis Tests</h2>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="flex justify-between mb-4">
        <button
          onClick={cargarEstadisticasGenerales}
          className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded flex items-center"
        >
          <FiBarChart2 className="mr-2" /> Estadísticas Generales
        </button>
      </div>

      {/* Selector de Test */}
      {!testSeleccionado && !mostrarEstadisticasGenerales && !mostrarEstadisticasTest && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
          {tests.map(test => (
            <div
              key={test.id}
              className="border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors flex flex-col justify-between"
              onClick={() => setTestSeleccionado(test)}
            >
              <div>
                <h3 className="font-semibold text-lg mb-2">{test.titulo}</h3>
                {test.descripcion && <p className="text-sm text-gray-600 mb-2 break-words">{test.descripcion}</p>}
                <p className="text-sm text-gray-500 mb-1">
                  Preguntas: {typeof test.numero_preguntas === 'number' ? test.numero_preguntas : 'Cargando...'}
                </p>
                <p className="text-xs text-gray-400">
                  Creado: {new Date(test.creado_en).toLocaleDateString('es-ES')}
                </p>
              </div>
              <div className="flex justify-between items-center mt-4">
                <button
                  onClick={(e) => {
                    e.stopPropagation(); // Evita que el click en el botón propague al div contenedor
                    setTestSeleccionado(test);
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                >
                  Realizar Test
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation(); // Evita que el click en el botón propague al div contenedor
                    cargarEstadisticasTest(test.id);
                  }}
                  className="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50"
                >
                  <FiPieChart className="mr-2" /> Estadísticas
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Estadísticas Generales */}
      {mostrarEstadisticasGenerales && estadisticasGenerales && (
        <div className="bg-white border rounded-lg p-6 mt-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-bold">Estadísticas Generales</h3>
            <button
              onClick={() => setMostrarEstadisticasGenerales(false)}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded"
            >
              Volver
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <FiAward className="text-blue-600 mr-2 text-xl" />
                <h4 className="font-semibold">Tests Realizados</h4>
              </div>
              <p className="text-3xl font-bold text-blue-700">{estadisticasGenerales.totalTests}</p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <FiCheck className="text-green-600 mr-2 text-xl" />
                <h4 className="font-semibold">Respuestas Correctas</h4>
              </div>
              <p className="text-3xl font-bold text-green-700">{estadisticasGenerales.totalRespuestasCorrectas}</p>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <FiX className="text-red-600 mr-2 text-xl" />
                <h4 className="font-semibold">Respuestas Incorrectas</h4>
              </div>
              <p className="text-3xl font-bold text-red-700">{estadisticasGenerales.totalRespuestasIncorrectas}</p>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <FiTrendingUp className="text-purple-600 mr-2 text-xl" />
                <h4 className="font-semibold">Porcentaje de Acierto</h4>
              </div>
              <p className="text-3xl font-bold text-purple-700">{estadisticasGenerales.porcentajeAcierto.toFixed(1)}%</p>
            </div>
          </div>
        </div>
      )}

      {/* Estadísticas de Test Específico */}
      {mostrarEstadisticasTest && estadisticasTest && (
        <div className="bg-white border rounded-lg p-6 mt-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-bold">Estadísticas del Test</h3>
            <button
              onClick={() => setMostrarEstadisticasTest(false)}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded"
            >
              Volver
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <FiClock className="text-blue-600 mr-2 text-xl" />
                <h4 className="font-semibold">Veces Realizado</h4>
              </div>
              <p className="text-3xl font-bold text-blue-700">{estadisticasTest.fechasRealizacion.length}</p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <FiCheck className="text-green-600 mr-2 text-xl" />
                <h4 className="font-semibold">Respuestas Correctas</h4>
              </div>
              <p className="text-3xl font-bold text-green-700">{estadisticasTest.totalCorrectas}</p>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <FiX className="text-red-600 mr-2 text-xl" />
                <h4 className="font-semibold">Respuestas Incorrectas</h4>
              </div>
              <p className="text-3xl font-bold text-red-700">{estadisticasTest.totalIncorrectas}</p>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <FiTrendingUp className="text-purple-600 mr-2 text-xl" />
                <h4 className="font-semibold">Porcentaje de Acierto</h4>
              </div>
              <p className="text-3xl font-bold text-purple-700">{estadisticasTest.porcentajeAcierto.toFixed(1)}%</p>
            </div>
          </div>

          {estadisticasTest.preguntasMasFalladas.length > 0 && (
            <div className="mt-6">
              <h4 className="font-semibold text-lg mb-3">Preguntas con más fallos:</h4>
              <div className="space-y-3">
                {estadisticasTest.preguntasMasFalladas.map((pregunta, index) => (
                  <div key={pregunta.preguntaId} className="border rounded-lg p-4">
                    <div className="flex items-start">
                      <div className="bg-red-100 text-red-800 rounded-full w-6 h-6 flex items-center justify-center mr-2 flex-shrink-0">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{pregunta.pregunta}</p>
                        <div className="flex items-center mt-2 text-sm">
                          <span className="text-red-600 mr-4">
                            <FiX className="inline mr-1" /> {pregunta.totalFallos} fallos
                          </span>
                          <span className="text-green-600">
                            <FiCheck className="inline mr-1" /> {pregunta.totalAciertos} aciertos
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Visor de Test */}
      {testSeleccionado && preguntas.length > 0 && !mostrarEstadisticasGenerales && !mostrarEstadisticasTest && (
        <div className="bg-white border rounded-lg shadow-md p-6 mt-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-bold">{testSeleccionado.titulo}</h3>
            <button
              onClick={() => setTestSeleccionado(null)}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded"
            >
              Volver
            </button>
          </div>

          {/* Resultados del Test */}
          {testCompletado && resultados && (
            <div className="bg-gray-50 border rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-lg mb-3">Resultados del Test</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <p className="text-sm font-medium text-green-800">Correctas</p>
                  <p className="text-2xl font-bold text-green-700">{resultados.correctas}</p>
                </div>
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-sm font-medium text-red-800">Incorrectas</p>
                  <p className="text-2xl font-bold text-red-700">{resultados.incorrectas}</p>
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-sm font-medium text-blue-800">Porcentaje</p>
                  <p className="text-2xl font-bold text-blue-700">{resultados.porcentaje.toFixed(1)}%</p>
                </div>
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                  <p className="text-sm font-medium text-purple-800">Tiempo Total</p>
                  <p className="text-2xl font-bold text-purple-700">{formatTiempo(resultados.tiempoTotal)}</p>
                </div>
              </div>
              <div className="mt-4 flex justify-center">
                <button
                  onClick={reiniciarTest}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded"
                >
                  Realizar de nuevo
                </button>
              </div>
            </div>
          )}

          {/* Pregunta Actual */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">
                Pregunta {preguntaActual + 1} de {preguntas.length}
              </span>
              {!testCompletado && (
                <span className="text-sm text-gray-500">
                  <FiClock className="inline mr-1" /> Tiempo por pregunta
                </span>
              )}
            </div>
            <div className="h-2 bg-gray-200 rounded-full mb-4">
              <div
                className="h-2 bg-indigo-600 rounded-full"
                style={{ width: `${((preguntaActual + 1) / preguntas.length) * 100}%` }}
              ></div>
            </div>
          </div>

          <div className="min-h-[300px]">
            <div className="mb-6">
              <h4 className="font-semibold text-lg mb-6">{preguntas[preguntaActual]?.pregunta}</h4>

              <div className="space-y-3 mt-6">
                {['a', 'b', 'c', 'd'].map((opcion) => {
                  const esCorrecta = testCompletado && opcion === preguntas[preguntaActual].respuesta_correcta;
                  const esIncorrecta = testCompletado && respuestaSeleccionada === opcion && opcion !== preguntas[preguntaActual].respuesta_correcta;
                  const esSeleccionada = respuestaSeleccionada === opcion;

                  return (
                    <div
                      key={opcion}
                      className={`p-3 border rounded-lg cursor-pointer ${
                        esCorrecta ? 'bg-green-100 border-green-500' :
                        esIncorrecta ? 'bg-red-100 border-red-500' :
                        esSeleccionada ? 'bg-indigo-100 border-indigo-500' :
                        'hover:bg-gray-50'
                      }`}
                      onClick={() => !testCompletado && seleccionarRespuesta(opcion as 'a' | 'b' | 'c' | 'd')}
                    >
                      <div className="flex items-start">
                        <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                          esCorrecta ? 'bg-green-500 text-white' :
                          esIncorrecta ? 'bg-red-500 text-white' :
                          esSeleccionada ? 'bg-indigo-500 text-white' :
                          'bg-gray-200 text-gray-700'
                        }`}>
                          {opcion.toUpperCase()}
                        </div>
                        <div className="flex-grow">
                          {preguntas[preguntaActual]?.[`opcion_${opcion}` as keyof PreguntaTest]}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <div className="flex justify-between mt-6">
            <button
              onClick={anteriorPregunta}
              disabled={preguntaActual === 0}
              className={`flex items-center ${
                preguntaActual === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-indigo-600 hover:text-indigo-800'
              }`}
            >
              <FiChevronLeft className="mr-1" /> Anterior
            </button>

            {!testCompletado && (
              <button
                onClick={siguientePregunta}
                disabled={!respuestaSeleccionada}
                className={`bg-indigo-600 text-white py-2 px-4 rounded flex items-center ${
                  !respuestaSeleccionada ? 'opacity-50 cursor-not-allowed' : 'hover:bg-indigo-700'
                }`}
              >
                {preguntaActual === preguntas.length - 1 ? 'Finalizar Test' : 'Siguiente'}
                {preguntaActual < preguntas.length - 1 && <FiChevronRight className="ml-1" />}
              </button>
            )}

            {testCompletado && preguntaActual < preguntas.length - 1 && (
              <button
                onClick={siguientePregunta}
                className="text-indigo-600 hover:text-indigo-800 flex items-center"
              >
                Siguiente <FiChevronRight className="ml-1" />
              </button>
            )}
          </div>
        </div>
      )}

      {/* Mensaje de carga */}
      {cargando && (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      )}

      {/* Mensaje de no hay tests */}
      {!cargando && tests.length === 0 && !mostrarEstadisticasGenerales && !mostrarEstadisticasTest && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded flex items-start">
          <FiAlertTriangle className="mr-2 mt-1 flex-shrink-0" />
          <div>
            <p className="font-medium">No hay tests disponibles</p>
            <p className="text-sm mt-1">Genera nuevos tests desde la sección "Generar Tests".</p>
          </div>
        </div>
      )}
    </div>
  );
}
