import React, { useState, useEffect } from 'react';
import {
  Documento,
  Test,
  PreguntaTest,
  crearTest,
  guardarPreguntasTest,
  obtenerTests,
  obtenerPreguntasTestCount
} from '../lib/supabase';
import type { PreguntaGenerada } from '../lib/gemini';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { testFormSchema } from '../lib/formSchemas';
import { useBackgroundGeneration } from '@/hooks/useBackgroundGeneration';
import { useBackgroundTasks } from '@/contexts/BackgroundTasksContext';
import { useTaskResults } from '@/hooks/useTaskResults';
import { toast } from 'react-hot-toast';

interface TestGeneratorProps {
  documentosSeleccionados: Documento[];
}

interface TestConContador extends Test {
  numPreguntas?: number;
}

export default function TestGenerator({ documentosSeleccionados }: TestGeneratorProps) {
  const [tituloTest, setTituloTest] = useState('');
  const [descripcionTest, setDescripcionTest] = useState('');
  const [preguntasGeneradas, setPreguntasGeneradas] = useState<PreguntaGenerada[]>([]);
  const [testGuardado, setTestGuardado] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [mostrarRespuesta, setMostrarRespuesta] = useState(false);
  const [testsExistentes, setTestsExistentes] = useState<TestConContador[]>([]);
  const [testSeleccionado, setTestSeleccionado] = useState<string>('nuevo');
  const [cargandoTests, setCargandoTests] = useState(false);
  const [error, setError] = useState('');

  const { generateTest, isGenerating, getActiveTask } = useBackgroundGeneration();
  const { getTask } = useBackgroundTasks();

  // Verificar si hay una tarea activa de test
  const activeTask = getActiveTask('test');
  const isLoading = isGenerating('test');

  // Suscribirse a los resultados de las tareas de tests
  useTaskResults({
    taskType: 'test',
    onResult: (result) => {
      setPreguntasGeneradas(result);
      toast.success('¡Test generado exitosamente!');
    },
    onError: (error) => {
      toast.error(`Error al generar test: ${error}`);
    }
  });

  const {
    register,
    handleSubmit: handleSubmitForm,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(testFormSchema),
    defaultValues: { peticion: '' }
  });

  // Cargar tests existentes al montar el componente
  useEffect(() => {
    cargarTests();
  }, []);

  const cargarTests = async () => {
    setCargandoTests(true);
    try {
      const testsData = await obtenerTests();

      // Obtener el número de preguntas para cada test
      const testsConContador: TestConContador[] = [];
      for (const test of testsData) {
        const numPreguntas = await obtenerPreguntasTestCount(test.id);
        testsConContador.push({
          ...test,
          numPreguntas
        });
      }

      setTestsExistentes(testsConContador);
    } catch (error) {
      console.error('Error al cargar tests:', error);
      toast.error('No se pudieron cargar los tests existentes.');
    } finally {
      setCargandoTests(false);
    }
  };

  const onSubmit = async (data: { peticion: string }) => {
    const contextos = documentosSeleccionados.map(doc => doc.contenido);
    setPreguntasGeneradas([]);
    setTestGuardado(false);

    try {
      await generateTest({
        peticion: data.peticion,
        contextos
      });

      // Establecer título si no existe
      if (!tituloTest) {
        setTituloTest(`Test: ${data.peticion.substring(0, 50)}${data.peticion.length > 50 ? '...' : ''}`);
      }

      // Mostrar mensaje informativo sobre la generación en segundo plano
      toast.success('Generación iniciada en segundo plano. Puedes continuar usando la aplicación.', {
        duration: 4000,
      });

    } catch (error) {
      toast.error('Error al iniciar la generación del test');
    }
  };

  const handleGuardarTest = async () => {
    if (preguntasGeneradas.length === 0) {
      setError('No hay preguntas para guardar');
      return;
    }

    // Si se seleccionó crear un nuevo test, validar el título
    if (testSeleccionado === 'nuevo' && !tituloTest.trim()) {
      setError('Por favor, proporciona un título para el nuevo test');
      return;
    }

    // Si se seleccionó un test existente, validar que se haya seleccionado uno
    if (testSeleccionado !== 'nuevo' && testSeleccionado === '') {
      setError('Por favor, selecciona un test existente');
      return;
    }

    setError('');

    try {
      let testId: string | null;

      // Si es un nuevo test, crearlo
      if (testSeleccionado === 'nuevo') {
        testId = await crearTest(
          tituloTest,
          descripcionTest,
          documentosSeleccionados.map(doc => doc.id)
        );

        if (!testId) {
          throw new Error('No se pudo crear el test');
        }
      } else {
        // Usar el test existente seleccionado
        testId = testSeleccionado;
      }

      // Preparar las preguntas para guardar
      const preguntasParaGuardar = preguntasGeneradas.map(pg => ({
        test_id: testId as string,
        pregunta: pg.pregunta,
        opcion_a: pg.opciones.a,
        opcion_b: pg.opciones.b,
        opcion_c: pg.opciones.c,
        opcion_d: pg.opciones.d,
        respuesta_correcta: pg.respuesta_correcta
      }));

      // Guardar las preguntas
      const resultado = await guardarPreguntasTest(preguntasParaGuardar);

      if (!resultado) {
        throw new Error('No se pudieron guardar las preguntas');
      }

      setTestGuardado(true);

      // Recargar los tests para tener la lista actualizada
      if (testSeleccionado === 'nuevo') {
        await cargarTests();
      }
    } catch (error) {
      console.error('Error al guardar las preguntas:', error);
      setError('Ha ocurrido un error al guardar las preguntas. Por favor, inténtalo de nuevo.');
    }
  };

  const handleNextQuestion = () => {
    if (activeIndex < preguntasGeneradas.length - 1) {
      setActiveIndex(activeIndex + 1);
      setMostrarRespuesta(false);
    }
  };

  const handlePrevQuestion = () => {
    if (activeIndex > 0) {
      setActiveIndex(activeIndex - 1);
      setMostrarRespuesta(false);
    }
  };

  const toggleRespuesta = () => {
    setMostrarRespuesta(!mostrarRespuesta);
  };

  return (
    <div className="mt-8 border-t pt-8">
      <h2 className="text-xl font-bold mb-4">Generador de Tests</h2>

      <form onSubmit={handleSubmitForm(onSubmit)} className="space-y-4">
        <div>
          <label htmlFor="peticion" className="block text-gray-700 text-sm font-bold mb-2">
            Describe el test que deseas generar:
          </label>
          <textarea
            id="peticion"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={3}
            {...register('peticion')}
            placeholder="Ej: Genera un test sobre los conceptos principales del tema 1"
            disabled={isLoading}
          />
          {errors.peticion && <span className="text-red-500 text-sm">{errors.peticion.message}</span>}
          <p className="text-sm text-gray-500 mt-1">
            La IA generará preguntas de test basadas en los documentos seleccionados y tu petición.
          </p>
        </div>

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}

        <div>
          <button
            type="submit"
            className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            disabled={isLoading || documentosSeleccionados.length === 0}
          >
            {isLoading ? 'Generando...' : 'Generar Test'}
          </button>
        </div>
      </form>

      {isLoading && (
        <div className="mt-4 text-center">
          <p className="text-gray-600">Generando test, por favor espera...</p>
          <div className="mt-2 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          </div>
        </div>
      )}

      {preguntasGeneradas.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-4">Test generado ({preguntasGeneradas.length} preguntas)</h3>

          {/* Formulario para guardar el test */}
          {!testGuardado && (
            <div className="bg-gray-100 p-4 rounded-lg mb-6">
              <h4 className="font-medium mb-2">Guardar preguntas de test</h4>

              {/* Selector de test */}
              <div className="mb-4">
                <label htmlFor="tipoTest" className="block text-sm font-medium text-gray-700 mb-1">
                  ¿Dónde quieres guardar estas preguntas?
                </label>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="nuevoTest"
                      name="tipoTest"
                      value="nuevo"
                      checked={testSeleccionado === 'nuevo'}
                      onChange={() => setTestSeleccionado('nuevo')}
                      className="mr-2"
                      disabled={isLoading}
                    />
                    <label htmlFor="nuevoTest" className="text-sm text-gray-700">
                      Crear nuevo test
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="testExistente"
                      name="tipoTest"
                      value="existente"
                      checked={testSeleccionado !== 'nuevo'}
                      onChange={() => {
                        // Seleccionar el primer test por defecto si hay alguno
                        if (testsExistentes.length > 0) {
                          setTestSeleccionado(testsExistentes[0].id);
                        } else {
                          setTestSeleccionado('');
                        }
                      }}
                      className="mr-2"
                      disabled={isLoading || testsExistentes.length === 0}
                    />
                    <label htmlFor="testExistente" className="text-sm text-gray-700">
                      Añadir a un test existente
                      {testsExistentes.length === 0 && (
                        <span className="text-gray-500 ml-2">(No hay tests disponibles)</span>
                      )}
                    </label>
                  </div>
                </div>
              </div>

              {/* Formulario para nuevo test */}
              {testSeleccionado === 'nuevo' && (
                <div className="space-y-3">
                  <div>
                    <label htmlFor="tituloTest" className="block text-sm font-medium text-gray-700 mb-1">
                      Título del nuevo test:
                    </label>
                    <input
                      type="text"
                      id="tituloTest"
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      value={tituloTest}
                      onChange={(e) => setTituloTest(e.target.value)}
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label htmlFor="descripcionTest" className="block text-sm font-medium text-gray-700 mb-1">
                      Descripción (opcional):
                    </label>
                    <textarea
                      id="descripcionTest"
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      rows={2}
                      value={descripcionTest}
                      onChange={(e) => setDescripcionTest(e.target.value)}
                      disabled={isLoading}
                    />
                  </div>
                </div>
              )}

              {/* Selector de test existente */}
              {testSeleccionado !== 'nuevo' && testsExistentes.length > 0 && (
                <div>
                  <label htmlFor="testExistenteSelect" className="block text-sm font-medium text-gray-700 mb-1">
                    Selecciona un test:
                  </label>
                  <select
                    id="testExistenteSelect"
                    className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value={testSeleccionado}
                    onChange={(e) => setTestSeleccionado(e.target.value)}
                    disabled={isLoading}
                  >
                    {testsExistentes.map(test => (
                      <option key={test.id} value={test.id}>
                        {test.titulo} {test.numPreguntas ? `(${test.numPreguntas} preguntas)` : ''}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Botón de guardar */}
              <div className="mt-4">
                <button
                  type="button"
                  onClick={handleGuardarTest}
                  className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  disabled={false}
                >
                  Guardar preguntas
                </button>
              </div>
            </div>
          )}

          {testGuardado && (
            <div className="bg-green-100 text-green-800 p-4 rounded-lg mb-6">
              <p className="font-medium">
                {testSeleccionado === 'nuevo'
                  ? '¡Nuevo test creado correctamente!'
                  : '¡Preguntas añadidas al test correctamente!'}
              </p>
              <p className="text-sm mt-1">Puedes acceder a {testSeleccionado === 'nuevo' ? 'él' : 'las preguntas'} desde la sección de "Mis Tests".</p>
            </div>
          )}

          {/* Visor de preguntas */}
          <div className="bg-white border rounded-lg shadow-md p-6 mb-4">
            <div className="flex justify-between items-center mb-4">
              <button
                onClick={handlePrevQuestion}
                disabled={activeIndex === 0}
                className={`p-2 rounded-full ${
                  activeIndex === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <span className="text-gray-600">
                Pregunta {activeIndex + 1} de {preguntasGeneradas.length}
              </span>
              <button
                onClick={handleNextQuestion}
                disabled={activeIndex === preguntasGeneradas.length - 1}
                className={`p-2 rounded-full ${
                  activeIndex === preguntasGeneradas.length - 1 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            <div className="min-h-[300px]">
              <div className="mb-4">
                <h4 className="font-semibold text-lg mb-4">{preguntasGeneradas[activeIndex]?.pregunta}</h4>

                <div className="space-y-3 mt-6">
                  {['a', 'b', 'c', 'd'].map((opcion) => (
                    <div
                      key={opcion}
                      className={`p-3 border rounded-lg ${
                        mostrarRespuesta && preguntasGeneradas[activeIndex].respuesta_correcta === opcion
                          ? 'bg-green-100 border-green-500'
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-start">
                        <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                          mostrarRespuesta && preguntasGeneradas[activeIndex].respuesta_correcta === opcion
                            ? 'bg-green-500 text-white'
                            : 'bg-gray-200 text-gray-700'
                        }`}>
                          {opcion.toUpperCase()}
                        </div>
                        <div className="flex-grow">
                          {preguntasGeneradas[activeIndex]?.opciones[opcion as keyof typeof preguntasGeneradas[0]['opciones']]}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-4 text-center">
              <button
                onClick={toggleRespuesta}
                className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                {mostrarRespuesta ? 'Ocultar respuesta' : 'Mostrar respuesta'}
              </button>
            </div>
          </div>

          {/* Lista de todas las preguntas */}
          <div className="mt-6">
            <h4 className="font-medium mb-2">Todas las preguntas:</h4>
            <div className="space-y-2">
              {preguntasGeneradas.map((pregunta, index) => (
                <div
                  key={index}
                  className={`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                    index === activeIndex ? 'border-indigo-500 bg-indigo-50' : ''
                  }`}
                  onClick={() => {
                    setActiveIndex(index);
                    setMostrarRespuesta(false);
                  }}
                >
                  <p className="font-medium">{index + 1}. {pregunta.pregunta}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
