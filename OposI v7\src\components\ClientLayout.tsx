'use client';

import React from 'react';
import { AuthProvider } from '@/contexts/AuthContext';
import AuthManager from '@/components/AuthManager';
import { Toaster } from 'react-hot-toast'; // Importar Toaster

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <AuthProvider>
      <AuthManager />
      <Toaster
        position="top-right" // Posición de los toasts
        toastOptions={{
          // Opciones por defecto para los toasts
          duration: 5000, // Duración de 5 segundos
          style: {
            background: '#363636', // Estilo oscuro
            color: '#fff',
          },
          success: {
            duration: 3000,
            style: {
              background: '#10b981',
              color: '#fff',
            },
          },
          error: {
            duration: 5000,
            style: {
              background: '#ef4444',
              color: '#fff',
            },
          }
        }}
      />
      {children}
    </AuthProvider>
  );
}
