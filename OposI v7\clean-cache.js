#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Script para limpiar la caché de Next.js y resolver problemas de compilación
 */

const foldersToClean = [
  '.next',
  'node_modules/.cache',
  '.vercel',
];

const filesToClean = [
  'tsconfig.tsbuildinfo',
];

function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    fs.rmSync(folderPath, { recursive: true, force: true });
    console.log(`✅ Eliminado: ${folderPath}`);
  } else {
    console.log(`⚠️  No existe: ${folderPath}`);
  }
}

function deleteFile(filePath) {
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`✅ Eliminado: ${filePath}`);
  } else {
    console.log(`⚠️  No existe: ${filePath}`);
  }
}

console.log('🧹 Limpiando caché de Next.js...\n');

// Limpiar carpetas
foldersToClean.forEach(folder => {
  deleteFolderRecursive(folder);
});

// Limpiar archivos
filesToClean.forEach(file => {
  deleteFile(file);
});

console.log('\n✨ Limpieza completada. Ahora puedes ejecutar:');
console.log('   npm run dev    (para desarrollo)');
console.log('   npm run build  (para producción)');
