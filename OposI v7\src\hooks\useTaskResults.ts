'use client';

import { useEffect, useState } from 'react';
import { useBackgroundTasks, BackgroundTask } from '@/contexts/BackgroundTasksContext';

interface UseTaskResultsOptions {
  taskType: BackgroundTask['type'];
  onResult?: (result: any) => void;
  onError?: (error: string) => void;
}

export const useTaskResults = ({ taskType, onResult, onError }: UseTaskResultsOptions) => {
  const { tasks } = useBackgroundTasks();
  const [lastProcessedTaskId, setLastProcessedTaskId] = useState<string | null>(null);

  useEffect(() => {
    // Buscar tareas completadas del tipo especificado que no hayamos procesado
    const completedTasks = tasks.filter(task => 
      task.type === taskType && 
      task.status === 'completed' && 
      task.id !== lastProcessedTaskId &&
      task.result
    );

    // Buscar tareas con error del tipo especificado que no hayamos procesado
    const errorTasks = tasks.filter(task => 
      task.type === taskType && 
      task.status === 'error' && 
      task.id !== lastProcessedTaskId &&
      task.error
    );

    // Procesar la tarea completada más reciente
    if (completedTasks.length > 0) {
      const latestTask = completedTasks[completedTasks.length - 1];
      setLastProcessedTaskId(latestTask.id);
      
      if (onResult) {
        onResult(latestTask.result);
      }
    }

    // Procesar la tarea con error más reciente
    if (errorTasks.length > 0) {
      const latestErrorTask = errorTasks[errorTasks.length - 1];
      setLastProcessedTaskId(latestErrorTask.id);
      
      if (onError) {
        onError(latestErrorTask.error!);
      }
    }
  }, [tasks, taskType, lastProcessedTaskId, onResult, onError]);

  // Función para resetear el último ID procesado (útil para procesar nuevos resultados)
  const resetLastProcessed = () => {
    setLastProcessedTaskId(null);
  };

  return {
    resetLastProcessed
  };
};
