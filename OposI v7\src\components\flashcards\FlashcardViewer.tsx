import React, { useState, useEffect } from 'react';
import {
  obtenerColeccionesFlashcards,
  obtenerFlashcardsParaEstudiar,
  registrarRespuestaFlashcard,
  obtenerEstadisticasColeccion,
  eliminarFlashcard,
  eliminarColeccionFlashcards,
  ColeccionFlashcards,
  FlashcardConProgreso,
  DificultadRespuesta
} from '@/lib/supabase';
import { FiEdit2, FiTrash2, FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';
import FlashcardCollectionList from './FlashcardCollectionList';
import FlashcardStatistics from './FlashcardStatistics';
import FlashcardStudyMode from './FlashcardStudyMode';
import FlashcardEditModal from './FlashcardEditModal';
import toast from 'react-hot-toast';

const FlashcardViewer: React.FC = () => {
  // Estado para las colecciones
  const [colecciones, setColecciones] = useState<ColeccionFlashcards[]>([]);
  const [coleccionSeleccionada, setColeccionSeleccionada] = useState<ColeccionFlashcards | null>(null);

  // Estado para las flashcards
  const [flashcards, setFlashcards] = useState<FlashcardConProgreso[]>([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [mostrarRespuesta, setMostrarRespuesta] = useState(false);
  const [respondiendo, setRespondiendo] = useState(false);

  // Estado para el modo de estudio
  const [modoEstudio, setModoEstudio] = useState(false);

  // Estado para estadísticas
  const [estadisticas, setEstadisticas] = useState<any>(null);

  // Estado para carga y errores
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Estado para edición y eliminación de flashcards
  const [flashcardEditando, setFlashcardEditando] = useState<FlashcardConProgreso | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Estado para eliminación de colecciones
  const [showDeleteCollectionConfirm, setShowDeleteCollectionConfirm] = useState<string | null>(null);
  const [deletingCollectionId, setDeletingCollectionId] = useState<string | null>(null);

  // Cargar colecciones al montar el componente
  useEffect(() => {
    const cargarColecciones = async () => {
      setIsLoading(true);
      try {
        const data = await obtenerColeccionesFlashcards();
        setColecciones(data);
      } catch (error) {
        console.error('Error al cargar colecciones:', error);
        setError('No se pudieron cargar las colecciones de flashcards');
      } finally {
        setIsLoading(false);
      }
    };

    cargarColecciones();
  }, []);

  // Manejar la selección de una colección
  const handleSeleccionarColeccion = async (coleccion: ColeccionFlashcards) => {
    setIsLoading(true);
    setError('');
    setColeccionSeleccionada(coleccion);
    setActiveIndex(0);
    setMostrarRespuesta(false);
    setRespondiendo(false);
    setModoEstudio(false);

    try {
      // Cargar flashcards con su progreso
      const data = await obtenerFlashcardsParaEstudiar(coleccion.id) as FlashcardConProgreso[];

      // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto
      const ordenadas = [...data].sort((a, b) => {
        if (a.debeEstudiar && !b.debeEstudiar) return -1;
        if (!a.debeEstudiar && b.debeEstudiar) return 1;
        return 0;
      });

      setFlashcards(ordenadas);

      // Cargar estadísticas
      const stats = await obtenerEstadisticasColeccion(coleccion.id);
      setEstadisticas(stats);
    } catch (error) {
      console.error('Error al cargar flashcards:', error);
      setError('No se pudieron cargar las flashcards de esta colección');
    } finally {
      setIsLoading(false);
    }
  };

  // Iniciar el modo de estudio
  const iniciarModoEstudio = async () => {
    setIsLoading(true);

    try {
      if (coleccionSeleccionada) {
        // Recargar las flashcards para asegurarnos de tener los datos más recientes
        const data = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);

        // Filtrar solo las flashcards que deben estudiarse hoy
        const flashcardsParaEstudiar = data.filter(flashcard => flashcard.debeEstudiar);

        // Actualizar estadísticas
        const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
        setEstadisticas(stats);

        // Verificar si el número de flashcards para estudiar coincide con las estadísticas
        if (flashcardsParaEstudiar.length !== stats.paraHoy) {
          console.warn(`Discrepancia en el conteo: ${flashcardsParaEstudiar.length} flashcards filtradas vs ${stats.paraHoy} en estadísticas`);
        }

        // Si no hay flashcards para hoy, mostrar un mensaje
        if (flashcardsParaEstudiar.length === 0) {
          if (data.length === 0) {
            alert('No hay flashcards en esta colección.');
          } else {
            alert('No hay flashcards programadas para estudiar hoy. Vuelve mañana o ajusta el progreso de las tarjetas.');
          }
          return; // Salir sin iniciar el modo estudio
        }

        // Usar solo las flashcards programadas para hoy
        setFlashcards(flashcardsParaEstudiar);

        // Iniciar el modo de estudio
        setModoEstudio(true);
        setActiveIndex(0);
        setMostrarRespuesta(false);
        setRespondiendo(false);
      }
    } catch (error) {
      console.error('Error al iniciar modo de estudio:', error);
      setError('No se pudo iniciar el modo de estudio');
    } finally {
      setIsLoading(false);
    }
  };

  // Manejar la navegación entre flashcards
  const handleNavigate = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && activeIndex > 0) {
      setActiveIndex(activeIndex - 1);
      setMostrarRespuesta(false);
    } else if (direction === 'next' && activeIndex < flashcards.length - 1) {
      setActiveIndex(activeIndex + 1);
      setMostrarRespuesta(false);
    }
  };

  // Manejar la respuesta a una flashcard
  const handleRespuesta = async (dificultad: DificultadRespuesta) => {
    if (!coleccionSeleccionada || flashcards.length === 0) return;

    const flashcardId = flashcards[activeIndex].id;
    setRespondiendo(true);

    try {
      // Registrar la respuesta
      await registrarRespuestaFlashcard(flashcardId, dificultad);

      // Recargar las flashcards y estadísticas si estamos en la última tarjeta
      if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {
        const data = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id) as FlashcardConProgreso[];
        const flashcardsParaEstudiar = data.filter(flashcard => flashcard.debeEstudiar);

        // Actualizar estadísticas
        const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
        setEstadisticas(stats);

        if (flashcardsParaEstudiar.length > 0) {
          setFlashcards(flashcardsParaEstudiar);
          setActiveIndex(0);
        } else {
          // Si no hay más flashcards para hoy, mostrar mensaje y salir del modo de estudio
          alert('¡Has completado todas las flashcards para hoy! Vuelve mañana para continuar estudiando.');
          setModoEstudio(false);

          // Ordenar las flashcards: primero las que deben estudiarse (aunque ya no haya ninguna), luego el resto
          const ordenadas = [...data].sort((a, b) => {
            if (a.debeEstudiar && !b.debeEstudiar) return -1;
            if (!a.debeEstudiar && b.debeEstudiar) return 1;
            return 0;
          });

          setFlashcards(ordenadas);
        }
      } else {
        // Avanzar a la siguiente flashcard
        handleNavigate('next');
      }
    } catch (error) {
      console.error('Error al registrar respuesta:', error);
      setError('No se pudo registrar la respuesta');
    } finally {
      setRespondiendo(false);
      setMostrarRespuesta(false);
    }
  };

  // Salir del modo de estudio
  const handleSalirModoEstudio = () => {
    setModoEstudio(false);
  };

  // Manejar la edición de una flashcard
  const handleEditarFlashcard = (flashcard: FlashcardConProgreso) => {
    setFlashcardEditando(flashcard);
    setShowEditModal(true);
  };

  // Manejar el guardado de una flashcard editada
  const handleGuardarFlashcard = (flashcardActualizada: FlashcardConProgreso) => {
    // Actualizar la flashcard en la lista local
    setFlashcards(prev =>
      prev.map(fc =>
        fc.id === flashcardActualizada.id ? flashcardActualizada : fc
      )
    );
  };

  // Manejar la eliminación de una flashcard
  const handleEliminarFlashcard = async (flashcardId: string) => {
    setDeletingId(flashcardId);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Eliminando flashcard...');

      const success = await eliminarFlashcard(flashcardId);

      if (success) {
        toast.success('Flashcard eliminada exitosamente', { id: loadingToastId });

        // Actualizar la lista local
        setFlashcards(prev => prev.filter(fc => fc.id !== flashcardId));

        // Recargar estadísticas
        if (coleccionSeleccionada) {
          const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
          setEstadisticas(stats);
        }
      } else {
        toast.error('Error al eliminar la flashcard', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al eliminar flashcard:', error);
      toast.error('Error al eliminar la flashcard', { id: loadingToastId });
    } finally {
      setDeletingId(null);
      setShowDeleteConfirm(null);
    }
  };

  // Manejar la eliminación de una colección completa
  const handleEliminarColeccion = (coleccionId: string) => {
    setShowDeleteCollectionConfirm(coleccionId);
  };

  const confirmarEliminarColeccion = async (coleccionId: string) => {
    setDeletingCollectionId(coleccionId);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Eliminando colección...');

      const success = await eliminarColeccionFlashcards(coleccionId);

      if (success) {
        toast.success('Colección eliminada exitosamente', { id: loadingToastId });

        // Actualizar la lista local de colecciones
        setColecciones(prev => prev.filter(col => col.id !== coleccionId));

        // Si la colección eliminada era la seleccionada, limpiar la selección
        if (coleccionSeleccionada?.id === coleccionId) {
          setColeccionSeleccionada(null);
          setFlashcards([]);
          setEstadisticas(null);
        }
      } else {
        toast.error('Error al eliminar la colección', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al eliminar colección:', error);
      toast.error('Error al eliminar la colección', { id: loadingToastId });
    } finally {
      setDeletingCollectionId(null);
      setShowDeleteCollectionConfirm(null);
    }
  };

  return (
    <div className="container mx-auto p-4">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {modoEstudio ? (
        <FlashcardStudyMode
          flashcards={flashcards}
          activeIndex={activeIndex}
          respondiendo={respondiendo}
          onRespuesta={handleRespuesta}
          onNavigate={handleNavigate}
          onVolver={handleSalirModoEstudio}
        />
      ) : (
        <div>
          <h2 className="text-2xl font-bold mb-4">Mis Flashcards</h2>

          <FlashcardCollectionList
            colecciones={colecciones}
            coleccionSeleccionada={coleccionSeleccionada}
            onSeleccionarColeccion={handleSeleccionarColeccion}
            onEliminarColeccion={handleEliminarColeccion}
            isLoading={isLoading}
            deletingId={deletingCollectionId}
          />

          {coleccionSeleccionada && (
            <div>
              <h3 className="text-xl font-semibold mb-4">{coleccionSeleccionada.titulo}</h3>

              <FlashcardStatistics estadisticas={estadisticas} />

              <div className="flex justify-between mb-6">
                <div>
                  <button
                    onClick={iniciarModoEstudio}
                    className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    {`Estudiar (${estadisticas ? estadisticas.paraHoy : 0} para hoy)`}
                  </button>
                </div>
                <div>
                  <button
                    onClick={() => {/* Implementar ver estadísticas detalladas */}}
                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    Ver estadísticas
                  </button>
                </div>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center h-40">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              ) : flashcards.length === 0 ? (
                <div className="text-center p-4">
                  <p className="text-gray-500">No hay flashcards en esta colección.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {flashcards.map((flashcard, index) => (
                    <div
                      key={flashcard.id}
                      className={`border rounded-lg p-4 ${
                        flashcard.debeEstudiar
                          ? 'border-orange-300 bg-orange-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <span className="text-sm text-gray-500">Tarjeta {index + 1}</span>
                        <div className="flex items-center space-x-2">
                          {flashcard.progreso?.estado && (
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                flashcard.progreso.estado === 'nuevo'
                                  ? 'bg-blue-100 text-blue-800'
                                  : flashcard.progreso.estado === 'aprendiendo'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : flashcard.progreso.estado === 'repasando'
                                  ? 'bg-orange-100 text-orange-800'
                                  : 'bg-green-100 text-green-800'
                              }`}
                            >
                              {flashcard.progreso.estado}
                            </span>
                          )}
                          {!flashcard.progreso?.estado && (
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                              nuevo
                            </span>
                          )}

                          {/* Botones de acción */}
                          <div className="flex space-x-1">
                            <button
                              onClick={() => handleEditarFlashcard(flashcard)}
                              className="p-1 text-blue-500 hover:bg-blue-50 rounded transition-colors"
                              title="Editar flashcard"
                            >
                              <FiEdit2 size={14} />
                            </button>
                            <button
                              onClick={() => setShowDeleteConfirm(flashcard.id)}
                              disabled={deletingId === flashcard.id}
                              className="p-1 text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50"
                              title="Eliminar flashcard"
                            >
                              {deletingId === flashcard.id ? (
                                <FiRefreshCw size={14} className="animate-spin" />
                              ) : (
                                <FiTrash2 size={14} />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                      <h4 className="font-medium mb-2">{flashcard.pregunta}</h4>
                      <p className="text-sm text-gray-700 line-clamp-2">{flashcard.respuesta}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Modal de edición */}
      {flashcardEditando && (
        <FlashcardEditModal
          flashcard={flashcardEditando}
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFlashcardEditando(null);
          }}
          onSave={handleGuardarFlashcard}
        />
      )}

      {/* Diálogo de confirmación de eliminación de flashcard */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <FiAlertTriangle className="text-red-500 mr-3" />
              <h3 className="text-lg font-semibold">Confirmar eliminación</h3>
            </div>

            <p className="text-gray-600 mb-6">
              ¿Estás seguro de que quieres eliminar esta flashcard? Esta acción no se puede deshacer y se perderá todo el progreso asociado.
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                disabled={deletingId !== null}
              >
                Cancelar
              </button>
              <button
                onClick={() => handleEliminarFlashcard(showDeleteConfirm)}
                className="px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors"
                disabled={deletingId !== null}
              >
                Eliminar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Diálogo de confirmación de eliminación de colección */}
      {showDeleteCollectionConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <FiAlertTriangle className="text-red-500 mr-3" />
              <h3 className="text-lg font-semibold">Confirmar eliminación de colección</h3>
            </div>

            <p className="text-gray-600 mb-6">
              ¿Estás seguro de que quieres eliminar esta colección completa? Esta acción no se puede deshacer y se eliminarán:
            </p>

            <ul className="text-gray-600 mb-6 list-disc list-inside space-y-1">
              <li>Todas las flashcards de la colección</li>
              <li>Todo el progreso de estudio</li>
              <li>El historial de revisiones</li>
              <li>Las estadísticas asociadas</li>
            </ul>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteCollectionConfirm(null)}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                disabled={deletingCollectionId !== null}
              >
                Cancelar
              </button>
              <button
                onClick={() => confirmarEliminarColeccion(showDeleteCollectionConfirm)}
                className="px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors"
                disabled={deletingCollectionId !== null}
              >
                Eliminar colección
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FlashcardViewer;
