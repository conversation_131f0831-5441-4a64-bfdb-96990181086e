[{"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\document\\upload\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\document\\upload\\__tests__\\route.test.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\gemini\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\gemini\\__tests__\\route.test.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\layout.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\login\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\AuthManager.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ClientLayout.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ConversationHistory.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DiagnosticPanel.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentManager.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentSelector.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentUploader.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\FlashcardGenerator.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardCollectionList.test.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardCollectionList.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardEditModal.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardStatistics.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardStudyMode.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardViewer.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\FlashcardViewer.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MindMapGenerator.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MobileDebugInfo.tsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ProtectedRoute.tsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\QuestionForm.tsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\RevisionHistory.tsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\StudyStatistics.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestGenerator.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestViewer.test.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestViewer.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\config\\prompts.ts": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\contexts\\AuthContext.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useMobileAuth.ts": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\formSchemas.ts": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\flashcardGenerator.ts": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\geminiClient.ts": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\index.ts": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\mindMapGenerator.ts": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\questionService.ts": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\testGenerator.ts": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\__tests__\\geminiClient.test.ts": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini.ts": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\authService.ts": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\client.ts": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\conversacionesService.ts": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\documentosService.server.ts": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\documentosService.ts": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\estadisticasService.ts": "49", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\flashcardsService.ts": "50", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\index.ts": "51", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\server.ts": "52", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\supabaseClient.ts": "53", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\testsService.ts": "54", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase.ts": "55", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\zodSchemas.ts": "56", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\middleware.ts": "57", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MindMapHelp.tsx": "58", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\BackgroundTasksPanel.tsx": "59", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\contexts\\BackgroundTasksContext.tsx": "60", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useBackgroundGeneration.ts": "61", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useTaskResults.ts": "62"}, {"size": 5950, "mtime": 1748553044080, "results": "63", "hashOfConfig": "64"}, {"size": 11389, "mtime": 1748376641049, "results": "65", "hashOfConfig": "64"}, {"size": 2346, "mtime": 1748384136441, "results": "66", "hashOfConfig": "64"}, {"size": 4614, "mtime": 1748376641237, "results": "67", "hashOfConfig": "64"}, {"size": 579, "mtime": 1748377333974, "results": "68", "hashOfConfig": "64"}, {"size": 6311, "mtime": 1748376641348, "results": "69", "hashOfConfig": "64"}, {"size": 11284, "mtime": 1748380781972, "results": "70", "hashOfConfig": "64"}, {"size": 2562, "mtime": 1748376641488, "results": "71", "hashOfConfig": "64"}, {"size": 1396, "mtime": 1748560022078, "results": "72", "hashOfConfig": "64"}, {"size": 3784, "mtime": 1748376641659, "results": "73", "hashOfConfig": "64"}, {"size": 8373, "mtime": 1748376641737, "results": "74", "hashOfConfig": "64"}, {"size": 6377, "mtime": 1748380672449, "results": "75", "hashOfConfig": "64"}, {"size": 2669, "mtime": 1748380001049, "results": "76", "hashOfConfig": "64"}, {"size": 10051, "mtime": 1748553148870, "results": "77", "hashOfConfig": "64"}, {"size": 17964, "mtime": 1748560941895, "results": "78", "hashOfConfig": "64"}, {"size": 4201, "mtime": 1748376642698, "results": "79", "hashOfConfig": "64"}, {"size": 3364, "mtime": 1748382572071, "results": "80", "hashOfConfig": "64"}, {"size": 6275, "mtime": 1748381218646, "results": "81", "hashOfConfig": "64"}, {"size": 1500, "mtime": 1748376642824, "results": "82", "hashOfConfig": "64"}, {"size": 10949, "mtime": 1748376642872, "results": "83", "hashOfConfig": "64"}, {"size": 20550, "mtime": 1748382672988, "results": "84", "hashOfConfig": "64"}, {"size": 26218, "mtime": 1748376642037, "results": "85", "hashOfConfig": "64"}, {"size": 11335, "mtime": 1748560861881, "results": "86", "hashOfConfig": "64"}, {"size": 5063, "mtime": 1748376642188, "results": "87", "hashOfConfig": "64"}, {"size": 1551, "mtime": 1748376642239, "results": "88", "hashOfConfig": "64"}, {"size": 19785, "mtime": 1748384161039, "results": "89", "hashOfConfig": "64"}, {"size": 4912, "mtime": 1748376642372, "results": "90", "hashOfConfig": "64"}, {"size": 15791, "mtime": 1748376642438, "results": "91", "hashOfConfig": "64"}, {"size": 18738, "mtime": 1748560903289, "results": "92", "hashOfConfig": "64"}, {"size": 6097, "mtime": 1748376642562, "results": "93", "hashOfConfig": "64"}, {"size": 24348, "mtime": 1748553228601, "results": "94", "hashOfConfig": "64"}, {"size": 25034, "mtime": 1748559096111, "results": "95", "hashOfConfig": "64"}, {"size": 7513, "mtime": 1748376643060, "results": "96", "hashOfConfig": "64"}, {"size": 3333, "mtime": 1748376643113, "results": "97", "hashOfConfig": "64"}, {"size": 1085, "mtime": 1748383742278, "results": "98", "hashOfConfig": "64"}, {"size": 1913, "mtime": 1748376643320, "results": "99", "hashOfConfig": "64"}, {"size": 5201, "mtime": 1748558738428, "results": "100", "hashOfConfig": "64"}, {"size": 2606, "mtime": 1748376643409, "results": "101", "hashOfConfig": "64"}, {"size": 3489, "mtime": 1748559525035, "results": "102", "hashOfConfig": "64"}, {"size": 2165, "mtime": 1748376643500, "results": "103", "hashOfConfig": "64"}, {"size": 2627, "mtime": 1748376643535, "results": "104", "hashOfConfig": "64"}, {"size": 18247, "mtime": 1748554831078, "results": "105", "hashOfConfig": "64"}, {"size": 195, "mtime": 1748376643201, "results": "106", "hashOfConfig": "64"}, {"size": 4813, "mtime": 1748376643664, "results": "107", "hashOfConfig": "64"}, {"size": 359, "mtime": 1748378344979, "results": "108", "hashOfConfig": "64"}, {"size": 8013, "mtime": 1748376643720, "results": "109", "hashOfConfig": "64"}, {"size": 1310, "mtime": 1748378808764, "results": "110", "hashOfConfig": "64"}, {"size": 3893, "mtime": 1748380964648, "results": "111", "hashOfConfig": "64"}, {"size": 10953, "mtime": 1748376643804, "results": "112", "hashOfConfig": "64"}, {"size": 17309, "mtime": 1748382512503, "results": "113", "hashOfConfig": "64"}, {"size": 269, "mtime": 1748376643883, "results": "114", "hashOfConfig": "64"}, {"size": 877, "mtime": 1748378355354, "results": "115", "hashOfConfig": "64"}, {"size": 3645, "mtime": 1748379019868, "results": "116", "hashOfConfig": "64"}, {"size": 8905, "mtime": 1748382249618, "results": "117", "hashOfConfig": "64"}, {"size": 197, "mtime": 1748376643238, "results": "118", "hashOfConfig": "64"}, {"size": 1317, "mtime": 1748383930683, "results": "119", "hashOfConfig": "64"}, {"size": 3608, "mtime": 1748378281175, "results": "120", "hashOfConfig": "64"}, {"size": 3779, "mtime": 1748557840096, "results": "121", "hashOfConfig": "64"}, {"size": 7292, "mtime": 1748559970572, "results": "122", "hashOfConfig": "64"}, {"size": 4632, "mtime": 1748561522824, "results": "123", "hashOfConfig": "64"}, {"size": 3738, "mtime": 1748560802983, "results": "124", "hashOfConfig": "64"}, {"size": 2651, "mtime": 1748561592350, "results": "125", "hashOfConfig": "64"}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7ro6ej", {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\document\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\document\\upload\\__tests__\\route.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\gemini\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\gemini\\__tests__\\route.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\AuthManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ClientLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ConversationHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DiagnosticPanel.tsx", ["312"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentUploader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\FlashcardGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardCollectionList.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardCollectionList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardStudyMode.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\FlashcardViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MindMapGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MobileDebugInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\QuestionForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\RevisionHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\StudyStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestViewer.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestViewer.tsx", ["313"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\config\\prompts.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useMobileAuth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\formSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\flashcardGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\geminiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\mindMapGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\questionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\testGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\__tests__\\geminiClient.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\conversacionesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\documentosService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\documentosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\flashcardsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\supabaseClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\testsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\zodSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MindMapHelp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\BackgroundTasksPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\contexts\\BackgroundTasksContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useBackgroundGeneration.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useTaskResults.ts", [], [], {"ruleId": "314", "severity": 1, "message": "315", "line": 127, "column": 6, "nodeType": "316", "endLine": 127, "endColumn": 14, "suggestions": "317"}, {"ruleId": "314", "severity": 1, "message": "318", "line": 67, "column": 6, "nodeType": "316", "endLine": 67, "endColumn": 17, "suggestions": "319"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'runDiagnostics'. Either include it or remove the dependency array.", "ArrayExpression", ["320"], "React Hook useEffect has a missing dependency: 'testCompletado'. Either include it or remove the dependency array.", ["321"], {"desc": "322", "fix": "323"}, {"desc": "324", "fix": "325"}, "Update the dependencies array to be: [isOpen, runDiagnostics]", {"range": "326", "text": "327"}, "Update the dependencies array to be: [preguntas, testCompletado]", {"range": "328", "text": "329"}, [4227, 4235], "[isOpen, runDiagnostics]", [2312, 2323], "[pregun<PERSON>, testCompletado]"]