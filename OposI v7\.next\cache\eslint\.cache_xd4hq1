[{"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\document\\upload\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\document\\upload\\__tests__\\route.test.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\gemini\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\gemini\\__tests__\\route.test.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\layout.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\login\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\AuthManager.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ClientLayout.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ConversationHistory.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DiagnosticPanel.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentManager.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentSelector.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentUploader.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\FlashcardGenerator.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardCollectionList.test.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardCollectionList.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardEditModal.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardStatistics.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardStudyMode.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardViewer.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\FlashcardViewer.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MindMapGenerator.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MobileDebugInfo.tsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ProtectedRoute.tsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\QuestionForm.tsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\RevisionHistory.tsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\StudyStatistics.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestGenerator.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestViewer.test.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestViewer.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\config\\prompts.ts": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\contexts\\AuthContext.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useMobileAuth.ts": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\formSchemas.ts": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\flashcardGenerator.ts": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\geminiClient.ts": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\index.ts": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\mindMapGenerator.ts": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\questionService.ts": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\testGenerator.ts": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\__tests__\\geminiClient.test.ts": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini.ts": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\authService.ts": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\client.ts": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\conversacionesService.ts": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\documentosService.server.ts": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\documentosService.ts": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\estadisticasService.ts": "49", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\flashcardsService.ts": "50", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\index.ts": "51", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\server.ts": "52", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\supabaseClient.ts": "53", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\testsService.ts": "54", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase.ts": "55", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\zodSchemas.ts": "56", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\middleware.ts": "57", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MindMapHelp.tsx": "58", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\BackgroundTasksPanel.tsx": "59", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\contexts\\BackgroundTasksContext.tsx": "60", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useBackgroundGeneration.ts": "61", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useTaskResults.ts": "62", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardDetailedStatistics.tsx": "63", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardGeneralStatistics.tsx": "64"}, {"size": 5950, "mtime": 1748553044080, "results": "65", "hashOfConfig": "66"}, {"size": 11389, "mtime": 1748376641049, "results": "67", "hashOfConfig": "66"}, {"size": 2346, "mtime": 1748384136441, "results": "68", "hashOfConfig": "66"}, {"size": 4614, "mtime": 1748376641237, "results": "69", "hashOfConfig": "66"}, {"size": 579, "mtime": 1748377333974, "results": "70", "hashOfConfig": "66"}, {"size": 6311, "mtime": 1748376641348, "results": "71", "hashOfConfig": "66"}, {"size": 11284, "mtime": 1748380781972, "results": "72", "hashOfConfig": "66"}, {"size": 2562, "mtime": 1748376641488, "results": "73", "hashOfConfig": "66"}, {"size": 1396, "mtime": 1748560022078, "results": "74", "hashOfConfig": "66"}, {"size": 3784, "mtime": 1748376641659, "results": "75", "hashOfConfig": "66"}, {"size": 8373, "mtime": 1748376641737, "results": "76", "hashOfConfig": "66"}, {"size": 6377, "mtime": 1748380672449, "results": "77", "hashOfConfig": "66"}, {"size": 2669, "mtime": 1748380001049, "results": "78", "hashOfConfig": "66"}, {"size": 10051, "mtime": 1748553148870, "results": "79", "hashOfConfig": "66"}, {"size": 17964, "mtime": 1748560941895, "results": "80", "hashOfConfig": "66"}, {"size": 4201, "mtime": 1748376642698, "results": "81", "hashOfConfig": "66"}, {"size": 3364, "mtime": 1748382572071, "results": "82", "hashOfConfig": "66"}, {"size": 6275, "mtime": 1748381218646, "results": "83", "hashOfConfig": "66"}, {"size": 1500, "mtime": 1748376642824, "results": "84", "hashOfConfig": "66"}, {"size": 10949, "mtime": 1748376642872, "results": "85", "hashOfConfig": "66"}, {"size": 21833, "mtime": 1748562446632, "results": "86", "hashOfConfig": "66"}, {"size": 26218, "mtime": 1748376642037, "results": "87", "hashOfConfig": "66"}, {"size": 11335, "mtime": 1748560861881, "results": "88", "hashOfConfig": "66"}, {"size": 5063, "mtime": 1748376642188, "results": "89", "hashOfConfig": "66"}, {"size": 1551, "mtime": 1748376642239, "results": "90", "hashOfConfig": "66"}, {"size": 19785, "mtime": 1748384161039, "results": "91", "hashOfConfig": "66"}, {"size": 4912, "mtime": 1748376642372, "results": "92", "hashOfConfig": "66"}, {"size": 15791, "mtime": 1748376642438, "results": "93", "hashOfConfig": "66"}, {"size": 18738, "mtime": 1748560903289, "results": "94", "hashOfConfig": "66"}, {"size": 6097, "mtime": 1748376642562, "results": "95", "hashOfConfig": "66"}, {"size": 24348, "mtime": 1748553228601, "results": "96", "hashOfConfig": "66"}, {"size": 25034, "mtime": 1748559096111, "results": "97", "hashOfConfig": "66"}, {"size": 7513, "mtime": 1748376643060, "results": "98", "hashOfConfig": "66"}, {"size": 3333, "mtime": 1748376643113, "results": "99", "hashOfConfig": "66"}, {"size": 1085, "mtime": 1748383742278, "results": "100", "hashOfConfig": "66"}, {"size": 1913, "mtime": 1748376643320, "results": "101", "hashOfConfig": "66"}, {"size": 5201, "mtime": 1748558738428, "results": "102", "hashOfConfig": "66"}, {"size": 2606, "mtime": 1748376643409, "results": "103", "hashOfConfig": "66"}, {"size": 3489, "mtime": 1748559525035, "results": "104", "hashOfConfig": "66"}, {"size": 2165, "mtime": 1748376643500, "results": "105", "hashOfConfig": "66"}, {"size": 2627, "mtime": 1748376643535, "results": "106", "hashOfConfig": "66"}, {"size": 18247, "mtime": 1748554831078, "results": "107", "hashOfConfig": "66"}, {"size": 195, "mtime": 1748376643201, "results": "108", "hashOfConfig": "66"}, {"size": 4813, "mtime": 1748376643664, "results": "109", "hashOfConfig": "66"}, {"size": 359, "mtime": 1748378344979, "results": "110", "hashOfConfig": "66"}, {"size": 8013, "mtime": 1748376643720, "results": "111", "hashOfConfig": "66"}, {"size": 1310, "mtime": 1748378808764, "results": "112", "hashOfConfig": "66"}, {"size": 3893, "mtime": 1748380964648, "results": "113", "hashOfConfig": "66"}, {"size": 10953, "mtime": 1748376643804, "results": "114", "hashOfConfig": "66"}, {"size": 18641, "mtime": 1748563118683, "results": "115", "hashOfConfig": "66"}, {"size": 269, "mtime": 1748376643883, "results": "116", "hashOfConfig": "66"}, {"size": 877, "mtime": 1748378355354, "results": "117", "hashOfConfig": "66"}, {"size": 3645, "mtime": 1748379019868, "results": "118", "hashOfConfig": "66"}, {"size": 8905, "mtime": 1748382249618, "results": "119", "hashOfConfig": "66"}, {"size": 197, "mtime": 1748376643238, "results": "120", "hashOfConfig": "66"}, {"size": 1317, "mtime": 1748383930683, "results": "121", "hashOfConfig": "66"}, {"size": 3608, "mtime": 1748378281175, "results": "122", "hashOfConfig": "66"}, {"size": 3779, "mtime": 1748557840096, "results": "123", "hashOfConfig": "66"}, {"size": 7292, "mtime": 1748559970572, "results": "124", "hashOfConfig": "66"}, {"size": 4632, "mtime": 1748561522824, "results": "125", "hashOfConfig": "66"}, {"size": 3738, "mtime": 1748560802983, "results": "126", "hashOfConfig": "66"}, {"size": 2651, "mtime": 1748561592350, "results": "127", "hashOfConfig": "66"}, {"size": 8927, "mtime": 1748562315253, "results": "128", "hashOfConfig": "66"}, {"size": 11220, "mtime": 1748562362912, "results": "129", "hashOfConfig": "66"}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7ro6ej", {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\document\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\document\\upload\\__tests__\\route.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\gemini\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\gemini\\__tests__\\route.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\AuthManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ClientLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ConversationHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DiagnosticPanel.tsx", ["322"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\DocumentUploader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\FlashcardGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardCollectionList.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardCollectionList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardStudyMode.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\FlashcardViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MindMapGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MobileDebugInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\QuestionForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\RevisionHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\StudyStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestViewer.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\TestViewer.tsx", ["323"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\config\\prompts.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useMobileAuth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\formSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\flashcardGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\geminiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\mindMapGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\questionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\testGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini\\__tests__\\geminiClient.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\gemini.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\conversacionesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\documentosService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\documentosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\flashcardsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\supabaseClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase\\testsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\lib\\zodSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\MindMapHelp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\BackgroundTasksPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\contexts\\BackgroundTasksContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useBackgroundGeneration.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\hooks\\useTaskResults.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardDetailedStatistics.tsx", ["324"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\flashcards\\FlashcardGeneralStatistics.tsx", [], [], {"ruleId": "325", "severity": 1, "message": "326", "line": 127, "column": 6, "nodeType": "327", "endLine": 127, "endColumn": 14, "suggestions": "328"}, {"ruleId": "325", "severity": 1, "message": "329", "line": 67, "column": 6, "nodeType": "327", "endLine": 67, "endColumn": 17, "suggestions": "330"}, {"ruleId": "325", "severity": 1, "message": "331", "line": 22, "column": 6, "nodeType": "327", "endLine": 22, "endColumn": 19, "suggestions": "332"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'runDiagnostics'. Either include it or remove the dependency array.", "ArrayExpression", ["333"], "React Hook useEffect has a missing dependency: 'testCompletado'. Either include it or remove the dependency array.", ["334"], "React Hook useEffect has a missing dependency: 'cargarEstadisticas'. Either include it or remove the dependency array.", ["335"], {"desc": "336", "fix": "337"}, {"desc": "338", "fix": "339"}, {"desc": "340", "fix": "341"}, "Update the dependencies array to be: [isOpen, runDiagnostics]", {"range": "342", "text": "343"}, "Update the dependencies array to be: [preguntas, testCompletado]", {"range": "344", "text": "345"}, "Update the dependencies array to be: [cargarEstadisticas, coleccionId]", {"range": "346", "text": "347"}, [4227, 4235], "[isOpen, runDiagnostics]", [2312, 2323], "[pregun<PERSON>, testCompletado]", [719, 732], "[cargarEstadisticas, coleccionId]"]